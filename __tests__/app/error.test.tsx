import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../test-utils'
import Error from '../../src/app/error'

// Mock the CustomError component
vi.mock('@dtbx/ui/components', () => ({
  CustomError: ({ error, reset }: { error: unknown; reset: () => void }) => (
    <div data-testid="custom-error">
      <div data-testid="error-message">{String(error)}</div>
      <button data-testid="reset-button" onClick={reset}>
        Reset
      </button>
    </div>
  ),
}))

describe('Error Component', () => {
  const mockReset = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render without crashing', () => {
    const error = new Error('Test error')
    
    expect(() => {
      render(<Error error={error} reset={mockReset} />)
    }).not.toThrow()
  })

  it('should render CustomError component', () => {
    const error = new Error('Test error')
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
  })

  it('should pass error prop to CustomError', () => {
    const error = new Error('Test error message')

    render(<Error error={error} reset={mockReset} />)

    // The CustomError component should receive the error object and display it appropriately
    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
  })

  it('should pass reset function to CustomError', () => {
    const error = new Error('Test error')
    
    render(<Error error={error} reset={mockReset} />)
    
    const resetButton = screen.getByTestId('reset-button')
    expect(resetButton).toBeInTheDocument()
    
    resetButton.click()
    expect(mockReset).toHaveBeenCalledTimes(1)
  })

  it('should handle string error', () => {
    const error = 'String error message'
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('String error message')
  })

  it('should handle null error', () => {
    const error = null
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('null')
  })

  it('should handle undefined error', () => {
    const error = undefined
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('undefined')
  })

  it('should handle object error', () => {
    const error = { message: 'Object error', code: 500 }
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('[object Object]')
  })

  it('should handle number error', () => {
    const error = 404
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('404')
  })

  it('should handle boolean error', () => {
    const error = true
    
    render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('error-message')).toHaveTextContent('true')
  })

  it('should be a client component', () => {
    // This test verifies the component is marked as 'use client'
    const error = new Error('Test error')
    
    expect(() => {
      render(<Error error={error} reset={mockReset} />)
    }).not.toThrow()
  })

  it('should have correct prop types', () => {
    const error = new Error('Test error')
    const reset = vi.fn()
    
    // TypeScript compilation would catch type errors, but we can test runtime behavior
    expect(() => {
      render(<Error error={error} reset={reset} />)
    }).not.toThrow()
  })

  it('should render consistently on multiple renders', () => {
    const error = new Error('Consistent error')
    
    const { rerender } = render(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    
    rerender(<Error error={error} reset={mockReset} />)
    
    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
  })

  it('should handle reset function being called multiple times', () => {
    const error = new Error('Test error')
    
    render(<Error error={error} reset={mockReset} />)
    
    const resetButton = screen.getByTestId('reset-button')
    
    resetButton.click()
    resetButton.click()
    resetButton.click()
    
    expect(mockReset).toHaveBeenCalledTimes(3)
  })
})
