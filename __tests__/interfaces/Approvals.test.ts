import { describe, it, expect } from 'vitest'
import { 
  ICardApprovalRequest, 
  IDiffValues, 
  IPagination,
  IMakerCheckerType 
} from '../../src/store/interfaces/Approvals'
import { createMockCardApprovalRequest, createMockDiffValues } from '../mocks/data'

describe('Approvals Interfaces Type Validation', () => {
  describe('IDiffValues interface', () => {
    it('should accept valid diff values data', () => {
      const validDiff: IDiffValues = createMockDiffValues({
        field: 'status',
        name: 'Card Status',
        oldValue: 'inactive',
        newValue: 'active',
      })

      expect(validDiff).toBeDefined()
      expect(validDiff.field).toBe('status')
      expect(validDiff.name).toBe('Card Status')
      expect(validDiff.oldValue).toBe('inactive')
      expect(validDiff.newValue).toBe('active')
    })

    it('should handle different field types', () => {
      const pinDiff: IDiffValues = createMockDiffValues({
        field: 'pin',
        name: 'PIN',
        oldValue: 'not set',
        newValue: 'set',
      })

      const limitDiff: IDiffValues = createMockDiffValues({
        field: 'creditLimit',
        name: 'Credit Limit',
        oldValue: '1000',
        newValue: '2000',
      })

      expect(pinDiff.field).toBe('pin')
      expect(pinDiff.name).toBe('PIN')
      expect(limitDiff.field).toBe('creditLimit')
      expect(limitDiff.name).toBe('Credit Limit')
    })

    it('should handle null and undefined values', () => {
      const diffWithNulls: IDiffValues = createMockDiffValues({
        field: 'address',
        name: 'Address',
        oldValue: null,
        newValue: '123 Main St',
      })

      const diffWithUndefined: IDiffValues = createMockDiffValues({
        field: 'phone',
        name: 'Phone Number',
        oldValue: undefined,
        newValue: '+1234567890',
      })

      expect(diffWithNulls.oldValue).toBeNull()
      expect(diffWithNulls.newValue).toBe('123 Main St')
      expect(diffWithUndefined.oldValue).toBeUndefined()
      expect(diffWithUndefined.newValue).toBe('+1234567890')
    })
  })

  describe('IMakerCheckerType interface', () => {
    it('should accept valid maker checker type data', () => {
      const validMakerCheckerType: IMakerCheckerType = {
        channel: 'CARDS',
        checkerPermissions: ['APPROVE_CARD_ACTIVATION', 'REJECT_CARD_ACTIVATION'],
        description: 'Card activation approval process',
        makerPermissions: ['REQUEST_CARD_ACTIVATION'],
        module: 'CARDS',
        name: 'CARD_ACTIVATION',
        overridePermissions: ['OVERRIDE_CARD_ACTIVATION'],
        type: 'ACTIVATE',
      }

      expect(validMakerCheckerType).toBeDefined()
      expect(validMakerCheckerType.channel).toBe('CARDS')
      expect(validMakerCheckerType.checkerPermissions).toContain('APPROVE_CARD_ACTIVATION')
      expect(validMakerCheckerType.checkerPermissions).toContain('REJECT_CARD_ACTIVATION')
      expect(validMakerCheckerType.description).toBe('Card activation approval process')
      expect(validMakerCheckerType.makerPermissions).toContain('REQUEST_CARD_ACTIVATION')
      expect(validMakerCheckerType.module).toBe('CARDS')
      expect(validMakerCheckerType.name).toBe('CARD_ACTIVATION')
      expect(validMakerCheckerType.overridePermissions).toContain('OVERRIDE_CARD_ACTIVATION')
      expect(validMakerCheckerType.type).toBe('ACTIVATE')
    })

    it('should handle empty permission arrays', () => {
      const makerCheckerWithEmptyPermissions: IMakerCheckerType = {
        channel: 'CARDS',
        checkerPermissions: [],
        description: 'Test process',
        makerPermissions: [],
        module: 'CARDS',
        name: 'TEST_PROCESS',
        overridePermissions: [],
        type: 'TEST',
      }

      expect(makerCheckerWithEmptyPermissions.checkerPermissions).toEqual([])
      expect(makerCheckerWithEmptyPermissions.makerPermissions).toEqual([])
      expect(makerCheckerWithEmptyPermissions.overridePermissions).toEqual([])
    })

    it('should handle different action types', () => {
      const activateType: IMakerCheckerType = {
        channel: 'CARDS',
        checkerPermissions: [],
        description: 'Activate card',
        makerPermissions: [],
        module: 'CARDS',
        name: 'CARD_ACTIVATION',
        overridePermissions: [],
        type: 'ACTIVATE',
      }

      const deactivateType: IMakerCheckerType = {
        channel: 'CARDS',
        checkerPermissions: [],
        description: 'Deactivate card',
        makerPermissions: [],
        module: 'CARDS',
        name: 'CARD_DEACTIVATION',
        overridePermissions: [],
        type: 'DEACTIVATE',
      }

      const pinResetType: IMakerCheckerType = {
        channel: 'CARDS',
        checkerPermissions: [],
        description: 'Reset PIN',
        makerPermissions: [],
        module: 'CARDS',
        name: 'PIN_RESET',
        overridePermissions: [],
        type: 'RESET_PIN',
      }

      expect(activateType.type).toBe('ACTIVATE')
      expect(deactivateType.type).toBe('DEACTIVATE')
      expect(pinResetType.type).toBe('RESET_PIN')
    })
  })

  describe('IPagination interface', () => {
    it('should accept valid pagination data', () => {
      const validPagination: IPagination = {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 100,
        totalNumberOfPages: 10,
      }

      expect(validPagination).toBeDefined()
      expect(validPagination.pageNumber).toBe(1)
      expect(validPagination.pageSize).toBe(10)
      expect(validPagination.totalElements).toBe(100)
      expect(validPagination.totalNumberOfPages).toBe(10)
    })

    it('should handle zero values', () => {
      const emptyPagination: IPagination = {
        pageNumber: 0,
        pageSize: 0,
        totalElements: 0,
        totalNumberOfPages: 0,
      }

      expect(emptyPagination.pageNumber).toBe(0)
      expect(emptyPagination.pageSize).toBe(0)
      expect(emptyPagination.totalElements).toBe(0)
      expect(emptyPagination.totalNumberOfPages).toBe(0)
    })

    it('should handle large numbers', () => {
      const largePagination: IPagination = {
        pageNumber: 999999,
        pageSize: 100000,
        totalElements: 99999999,
        totalNumberOfPages: 999999,
      }

      expect(largePagination.pageNumber).toBe(999999)
      expect(largePagination.pageSize).toBe(100000)
      expect(largePagination.totalElements).toBe(99999999)
      expect(largePagination.totalNumberOfPages).toBe(999999)
    })
  })

  describe('ICardApprovalRequest interface', () => {
    it('should accept valid approval request data', () => {
      const validRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        id: 'approval-123',
        maker: 'John Doe',
        makerFirstName: 'John',
        makerLastName: 'Doe',
        dateCreated: '2023-01-01T10:00:00Z',
        dateModified: '2023-01-01T12:00:00Z',
        makerCheckerType: {
          channel: 'CARDS',
          checkerPermissions: ['APPROVE_CARD'],
          description: 'Card approval',
          makerPermissions: ['REQUEST_CARD'],
          module: 'CARDS',
          name: 'CARD_APPROVAL',
          overridePermissions: [],
          type: 'APPROVE',
        },
        entityId: 'card-456',
        entity: 'CreditCard',
        diff: [
          createMockDiffValues({
            field: 'status',
            name: 'Status',
            oldValue: 'pending',
            newValue: 'approved',
          }),
        ],
        makerComments: 'Please approve this card',
        status: 'PENDING',
        checker: undefined,
        checkerComments: undefined,
      })

      expect(validRequest).toBeDefined()
      expect(validRequest.id).toBe('approval-123')
      expect(validRequest.maker).toBe('John Doe')
      expect(validRequest.makerFirstName).toBe('John')
      expect(validRequest.makerLastName).toBe('Doe')
      expect(validRequest.entityId).toBe('card-456')
      expect(validRequest.entity).toBe('CreditCard')
      expect(validRequest.makerComments).toBe('Please approve this card')
      expect(validRequest.status).toBe('PENDING')
      expect(validRequest.checker).toBeUndefined()
      expect(validRequest.checkerComments).toBeUndefined()
      expect(validRequest.diff).toHaveLength(1)
      expect(validRequest.diff[0].field).toBe('status')
    })

    it('should handle approved request with checker data', () => {
      const approvedRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        id: 'approval-456',
        status: 'APPROVED',
        checker: 'Jane Smith',
        checkerComments: 'Approved after review',
        dateModified: '2023-01-02T14:00:00Z',
      })

      expect(approvedRequest.status).toBe('APPROVED')
      expect(approvedRequest.checker).toBe('Jane Smith')
      expect(approvedRequest.checkerComments).toBe('Approved after review')
    })

    it('should handle rejected request', () => {
      const rejectedRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        id: 'approval-789',
        status: 'REJECTED',
        checker: 'Bob Wilson',
        checkerComments: 'Rejected due to insufficient documentation',
      })

      expect(rejectedRequest.status).toBe('REJECTED')
      expect(rejectedRequest.checker).toBe('Bob Wilson')
      expect(rejectedRequest.checkerComments).toBe('Rejected due to insufficient documentation')
    })

    it('should handle multiple diff values', () => {
      const requestWithMultipleDiffs: ICardApprovalRequest = createMockCardApprovalRequest({
        diff: [
          createMockDiffValues({
            field: 'status',
            name: 'Status',
            oldValue: 'inactive',
            newValue: 'active',
          }),
          createMockDiffValues({
            field: 'creditLimit',
            name: 'Credit Limit',
            oldValue: '1000',
            newValue: '2000',
          }),
          createMockDiffValues({
            field: 'pin',
            name: 'PIN',
            oldValue: 'not set',
            newValue: 'set',
          }),
        ],
      })

      expect(requestWithMultipleDiffs.diff).toHaveLength(3)
      expect(requestWithMultipleDiffs.diff[0].field).toBe('status')
      expect(requestWithMultipleDiffs.diff[1].field).toBe('creditLimit')
      expect(requestWithMultipleDiffs.diff[2].field).toBe('pin')
    })

    it('should handle empty diff array', () => {
      const requestWithNoDiffs: ICardApprovalRequest = createMockCardApprovalRequest({
        diff: [],
      })

      expect(requestWithNoDiffs.diff).toEqual([])
    })

    it('should handle null and undefined optional fields', () => {
      const requestWithNulls: ICardApprovalRequest = createMockCardApprovalRequest({
        entityId: null,
        entity: null,
        makerComments: null,
        checker: null,
        checkerComments: null,
      })

      expect(requestWithNulls.entityId).toBeNull()
      expect(requestWithNulls.entity).toBeNull()
      expect(requestWithNulls.makerComments).toBeNull()
      expect(requestWithNulls.checker).toBeNull()
      expect(requestWithNulls.checkerComments).toBeNull()
    })
  })

  describe('Status validation', () => {
    it('should accept valid status values', () => {
      const pendingRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        status: 'PENDING',
      })

      const approvedRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        status: 'APPROVED',
      })

      const rejectedRequest: ICardApprovalRequest = createMockCardApprovalRequest({
        status: 'REJECTED',
      })

      expect(pendingRequest.status).toBe('PENDING')
      expect(approvedRequest.status).toBe('APPROVED')
      expect(rejectedRequest.status).toBe('REJECTED')
    })
  })

  describe('Date validation', () => {
    it('should handle ISO date strings', () => {
      const requestWithDates: ICardApprovalRequest = createMockCardApprovalRequest({
        dateCreated: '2023-01-01T10:00:00.000Z',
        dateModified: '2023-01-01T12:00:00.000Z',
      })

      expect(requestWithDates.dateCreated).toBe('2023-01-01T10:00:00.000Z')
      expect(requestWithDates.dateModified).toBe('2023-01-01T12:00:00.000Z')
      
      // Verify they are valid date strings
      expect(new Date(requestWithDates.dateCreated).getTime()).not.toBeNaN()
      expect(new Date(requestWithDates.dateModified).getTime()).not.toBeNaN()
    })
  })
})
