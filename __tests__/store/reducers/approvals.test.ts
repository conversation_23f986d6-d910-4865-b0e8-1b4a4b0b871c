import { describe, it, expect } from 'vitest'
import approvalsReducer, {
  setIsLoadingApprovals,
  setCardApprovalRequests,
  setBranchApprovalRequests,
  setSelectedCardApprovalRequest,
  setCardApprovalsPagination,
  resetCardApprovalRequests,
} from '../../../src/store/reducers/approvals'
import { ICardApprovalRequest, IPagination } from '../../../src/store/interfaces/Approvals'

const mockApprovalRequest: ICardApprovalRequest = {
  id: '0fa4f401-d171-4065-94ee-e36896c835ff',
  checker: null,
  maker: '<PERSON>',
  makerCheckerType: {
    name: 'Activate Card',
    type: 'ACTIVATE_CARDS',
    description: null,
    module: 'Cards',
    channel: 'CARDS',
    checkerPermissions: ['ACCEPT_ACTIVATE_CARDS'],
    makerPermissions: ['MAKE_ACTIVATE_CARDS'],
    overridePermissions: ['SUPER_ACTIVATE_CARDS'],
  },
  diff: [
    {
      field: 'active',
      oldValue: 'false',
      newValue: 'true',
    },
  ],
  status: 'PENDING',
  makerComments: 'New Card Issuance,activate',
  checkerComments: null,
  dateCreated: '2025-07-23 12:12:34',
  dateModified: '2025-07-23 12:12:34',
  entityId: '52936067126054847939',
  entity: '{"comments":"New Card Issuance,activate"}',
}

const mockPagination: IPagination = {
  pageNumber: 1,
  pageSize: 10,
  totalElements: 1,
  totalNumberOfPages: 1,
}

const initialState = {
  isLoadingApprovals: false,
  cardApprovalRequests: [],
  branchApprovalRequests: [],
  selectedCardApprovalRequest: {} as ICardApprovalRequest,
  cardApprovalsPagination: {} as IPagination,
}

describe('approvals reducer', () => {
  it('should return the initial state', () => {
    expect(approvalsReducer(undefined, { type: 'unknown' })).toEqual(initialState)
  })

  it('should handle setIsLoadingApprovals', () => {
    const actual = approvalsReducer(initialState, setIsLoadingApprovals(true))
    expect(actual.isLoadingApprovals).toBe(true)

    const actualFalse = approvalsReducer(actual, setIsLoadingApprovals(false))
    expect(actualFalse.isLoadingApprovals).toBe(false)
  })

  it('should handle setCardApprovalRequests', () => {
    const approvalRequests = [mockApprovalRequest]
    const actual = approvalsReducer(initialState, setCardApprovalRequests(approvalRequests))
    
    expect(actual.cardApprovalRequests).toEqual(approvalRequests)
    expect(actual.cardApprovalRequests).toHaveLength(1)
    expect(actual.cardApprovalRequests[0].id).toBe('0fa4f401-d171-4065-94ee-e36896c835ff')
  })

  it('should handle setBranchApprovalRequests', () => {
    const branchApprovalRequests = [mockApprovalRequest]
    const actual = approvalsReducer(initialState, setBranchApprovalRequests(branchApprovalRequests))
    
    expect(actual.branchApprovalRequests).toEqual(branchApprovalRequests)
    expect(actual.branchApprovalRequests).toHaveLength(1)
    expect(actual.branchApprovalRequests[0].status).toBe('PENDING')
    expect(actual.branchApprovalRequests[0].maker).toBe('Leroy Ombiji')
  })

  it('should handle setSelectedCardApprovalRequest', () => {
    const actual = approvalsReducer(initialState, setSelectedCardApprovalRequest(mockApprovalRequest))
    
    expect(actual.selectedCardApprovalRequest).toEqual(mockApprovalRequest)
    expect(actual.selectedCardApprovalRequest.id).toBe('0fa4f401-d171-4065-94ee-e36896c835ff')
    expect(actual.selectedCardApprovalRequest.makerCheckerType.name).toBe('Activate Card')
  })

  it('should handle setCardApprovalsPagination', () => {
    const actual = approvalsReducer(initialState, setCardApprovalsPagination(mockPagination))
    
    expect(actual.cardApprovalsPagination).toEqual(mockPagination)
    expect(actual.cardApprovalsPagination.pageNumber).toBe(1)
    expect(actual.cardApprovalsPagination.totalElements).toBe(1)
  })

  it('should handle resetCardApprovalRequests', () => {
    const stateWithData = {
      isLoadingApprovals: true,
      cardApprovalRequests: [mockApprovalRequest],
      branchApprovalRequests: [mockApprovalRequest],
      selectedCardApprovalRequest: mockApprovalRequest,
      cardApprovalsPagination: mockPagination,
    }
    
    const actual = approvalsReducer(stateWithData, resetCardApprovalRequests())
    expect(actual).toEqual(initialState)
  })

  it('should handle multiple setBranchApprovalRequests calls', () => {
    const firstRequest = { ...mockApprovalRequest, id: 'first-id' }
    const secondRequest = { ...mockApprovalRequest, id: 'second-id', status: 'APPROVED' }
    
    let state = approvalsReducer(initialState, setBranchApprovalRequests([firstRequest]))
    expect(state.branchApprovalRequests).toHaveLength(1)
    
    state = approvalsReducer(state, setBranchApprovalRequests([firstRequest, secondRequest]))
    expect(state.branchApprovalRequests).toHaveLength(2)
    expect(state.branchApprovalRequests[1].status).toBe('APPROVED')
  })

  it('should handle empty arrays for approval requests', () => {
    const stateWithData = {
      ...initialState,
      cardApprovalRequests: [mockApprovalRequest],
      branchApprovalRequests: [mockApprovalRequest],
    }
    
    let state = approvalsReducer(stateWithData, setCardApprovalRequests([]))
    expect(state.cardApprovalRequests).toEqual([])
    
    state = approvalsReducer(state, setBranchApprovalRequests([]))
    expect(state.branchApprovalRequests).toEqual([])
  })

  it('should preserve other state when updating specific fields', () => {
    const stateWithData = {
      isLoadingApprovals: true,
      cardApprovalRequests: [mockApprovalRequest],
      branchApprovalRequests: [],
      selectedCardApprovalRequest: mockApprovalRequest,
      cardApprovalsPagination: mockPagination,
    }
    
    const actual = approvalsReducer(stateWithData, setBranchApprovalRequests([mockApprovalRequest]))
    
    // Should update branchApprovalRequests
    expect(actual.branchApprovalRequests).toEqual([mockApprovalRequest])
    
    // Should preserve other state
    expect(actual.isLoadingApprovals).toBe(true)
    expect(actual.cardApprovalRequests).toEqual([mockApprovalRequest])
    expect(actual.selectedCardApprovalRequest).toEqual(mockApprovalRequest)
    expect(actual.cardApprovalsPagination).toEqual(mockPagination)
  })
})
