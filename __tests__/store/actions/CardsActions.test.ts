import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Dispatch } from '@reduxjs/toolkit'
import {
  activateCards,
  completeCardActivation,
  resetCardPin,
  completeCardSetPin,
  getAllCards,
  getPan,
  getCardById,
  getBranchApprovals,
  getBranchCardsByPhonePan,
  resetCardPinRetryCounter,
  completeResetCardPinRetryCounter,
  CardsQueryParams,
  BranchCardParams,
} from '../../../src/store/actions/CardsActions'
import {
  setIsLoadingActivateCard,
  setIsLoadingCards,
  setIsLoadingSingleCard,
  setIsLoadingSetPinCard,
  setIsLoadingBranchCards,
  setIsLoadingResetPinRetries,
  setIsLoadingPan,
  setCreditCardsList,
  setCardResponse,
  setSelectedCardToView,
  setBranchCardsList,
  setBranchCardsListPagination,
} from '../../../src/store/reducers/cards'
import { setBranchApprovalRequests } from '../../../src/store/reducers/approvals'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi } from '@dtbx/store/utils'
import { createMockCreditCard, createMockCardResponse } from '../../mocks/data'

// Mock dependencies
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
  },
}))

vi.mock('../../../src/store/reducers/cards', () => ({
  setIsLoadingActivateCard: vi.fn(),
  setIsLoadingCards: vi.fn(),
  setIsLoadingSingleCard: vi.fn(),
  setIsLoadingSetPinCard: vi.fn(),
  setIsLoadingBranchCards: vi.fn(),
  setIsLoadingResetPinRetries: vi.fn(),
  setIsLoadingPan: vi.fn(),
  setCreditCardsList: vi.fn(),
  setCardResponse: vi.fn(),
  setSelectedCardToView: vi.fn(),
  setBranchCardsList: vi.fn(),
  setBranchCardsListPagination: vi.fn(),
}))

vi.mock('../../../src/store/reducers/approvals', () => ({
  setBranchApprovalRequests: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn(),
}))

describe('CardsActions', () => {
  let mockDispatch: Dispatch

  beforeEach(() => {
    mockDispatch = vi.fn()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('activateCards', () => {
    const mockData = {
      comments: 'Test activation',
      cardIdentifierId: 'card123',
      cardIdentifierType: 'cardId',
      countryCode: 'KE',
    }

    it('should successfully activate card for super user', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

      await activateCards(mockDispatch, mockData, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(true))
      expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/activate', mockData)
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card activated successfully',
          type: 'success',
        })
      )
    })

    it('should successfully send activation request for regular user', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

      await activateCards(mockDispatch, mockData, 'regular')

      expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/activate/make', mockData)
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card activation request sent successfully',
          type: 'success',
        })
      )
    })

    it('should handle activation error', async () => {
      const mockError = new Error('Activation failed')
      vi.mocked(secureapi.post).mockRejectedValue(mockError)

      await activateCards(mockDispatch, mockData, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: 'Activation failed', type: 'error' })
      )
    })
  })

  describe('completeCardActivation', () => {
    const approvalId = 'approval123'
    const data = { comments: 'Approved' }

    it('should successfully approve card activation', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

      await completeCardActivation(mockDispatch, approvalId, 'approve', data)

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/activate/approve/${approvalId}`,
        data
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Activate credit card request has been approved. Customer will be notified via an sms that will include their PIN.',
          type: 'success',
        })
      )
    })

    it('should successfully reject card activation', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

      await completeCardActivation(mockDispatch, approvalId, 'reject', data)

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/activate/reject/${approvalId}`,
        data
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Activate credit card request has been rejected',
          type: 'success',
        })
      )
    })

    it('should handle completion error', async () => {
      const mockError = new Error('Completion failed')
      vi.mocked(secureapi.put).mockRejectedValue(mockError)

      await completeCardActivation(mockDispatch, approvalId, 'approve', data)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: 'Completion failed', type: 'error' })
      )
    })
  })

  describe('resetCardPin', () => {
    const mockData = {
      comments: 'Reset PIN',
      cardIdentifierId: 'card123',
      cardIdentifierType: 'cardId',
      countryCode: 'KE',
    }

    it('should successfully reset PIN for super user', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

      await resetCardPin(mockDispatch, mockData, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(true))
      expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/set-pin', mockData)
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card Pin reset successfully',
          type: 'success',
        })
      )
    })

    it('should successfully send PIN reset request for regular user', async () => {
      const mockResponse = { data: { success: true } }
      vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

      await resetCardPin(mockDispatch, mockData, 'regular')

      expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/set-pin/make', mockData)
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card Pin  request sent successfully',
          type: 'success',
        })
      )
    })

    it('should handle PIN reset error', async () => {
      const mockError = new Error('PIN reset failed')
      vi.mocked(secureapi.post).mockRejectedValue(mockError)

      await resetCardPin(mockDispatch, mockData, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: 'PIN reset failed', type: 'error' })
      )
    })
  })

  describe('getBranchApprovals', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

  const mockParams = {
    pan: '1234',
    phoneNumber: '+254712345678',
    page: 1,
    size: 10,
  }

  const mockApiResponse = {
    data: {
      data: [
        {
          id: '0fa4f401-d171-4065-94ee-e36896c835ff',
          checker: null,
          maker: 'Leroy Ombiji',
          makerCheckerType: {
            name: 'Activate Card',
            type: 'ACTIVATE_CARDS',
            description: null,
            module: 'Cards',
            channel: 'CARDS',
          },
          status: 'PENDING',
          makerComments: 'New Card Issuance,activate',
          checkerComments: null,
          dateCreated: '2025-07-23 12:12:34',
          dateModified: '2025-07-23 12:12:34',
          entityId: '52936067126054847939',
        },
      ],
    },
  }

  it('should successfully fetch branch approvals and dispatch setBranchApprovalRequests', async () => {
    vi.mocked(secureapi.get).mockResolvedValue(mockApiResponse)

    await getBranchApprovals(mockDispatch, mockParams)

    // Verify the API was called with correct URL and query parameters
    expect(secureapi.get).toHaveBeenCalledWith(
      'backoffice-bff/cards/branch-query/approvals?pan=1234&phoneNumber=%2B254712345678&page=1&size=10'
    )

    // Verify the dispatch was called with the correct action and data
    expect(setBranchApprovalRequests).toHaveBeenCalledWith(mockApiResponse.data.data)
  })

  it('should handle URL encoding of special characters in parameters', async () => {
    const paramsWithSpecialChars = {
      pan: '1234',
      phoneNumber: '+254 712 345 678',
      page: 1,
      size: 10,
    }

    vi.mocked(secureapi.get).mockResolvedValue(mockApiResponse)

    await getBranchApprovals(mockDispatch, paramsWithSpecialChars)

    expect(secureapi.get).toHaveBeenCalledWith(
      'backoffice-bff/cards/branch-query/approvals?pan=1234&phoneNumber=%2B254+712+345+678&page=1&size=10'
    )
  })

  it('should handle API errors and dispatch error notification', async () => {
    const mockError = new Error('Network error')
    vi.mocked(secureapi.get).mockRejectedValue(mockError)

    await getBranchApprovals(mockDispatch, mockParams)

    expect(setNotification).toHaveBeenCalledWith({
      message: 'Network error',
      type: 'error',
    })
  })
})

describe('getAllCards', () => {
  const mockParams: CardsQueryParams = {
    size: 10,
    page: 1,
    cardType: 'Credit',
    phoneNumber: '+254712345678',
  }

  it('should successfully fetch all cards', async () => {
    const mockResponse = createMockCardResponse()
    vi.mocked(secureapi.get).mockResolvedValue({ data: { data: mockResponse } })

    await getAllCards(mockDispatch, mockParams)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCards(true))
    expect(secureapi.get).toHaveBeenCalledWith('backoffice-bff/cards', { params: mockParams })
    //@ts-ignore
    expect(mockDispatch).toHaveBeenCalledWith(setCreditCardsList(mockResponse.data))
    expect(mockDispatch).toHaveBeenCalledWith(setCardResponse(mockResponse))
    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCards(false))
  })

  it('should handle fetch cards error', async () => {
    const mockError = new Error('Fetch failed')
    vi.mocked(secureapi.get).mockRejectedValue(mockError)

    await getAllCards(mockDispatch, mockParams)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCards(false))
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Fetch failed', type: 'error' })
    )
  })
})

describe('getCardById', () => {
  const cardId = 'card123'

  it('should successfully fetch card by ID', async () => {
    const mockCard = createMockCreditCard({ cardId })
    vi.mocked(secureapi.get).mockResolvedValue({ data: { data: mockCard } })

    await getCardById(mockDispatch, cardId)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSingleCard(true))
    expect(secureapi.get).toHaveBeenCalledWith(`backoffice-bff/cards/${cardId}`)
    expect(mockDispatch).toHaveBeenCalledWith(setSelectedCardToView(mockCard))
    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSingleCard(false))
  })

  it('should handle fetch card by ID error', async () => {
    const mockError = new Error('Card not found')
    vi.mocked(secureapi.get).mockRejectedValue(mockError)

    await getCardById(mockDispatch, cardId)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSingleCard(false))
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Card not found', type: 'error' })
    )
  })
})

describe('getPan', () => {
  const mockData = {
    publicKey: 'test-key',
    cardIDto: {
      cardIdentifierId: 'card123',
      cardIdentifierType: 'cardId',
      countryCode: 'KE',
    },
  }

  it('should successfully get PAN', async () => {
    const mockEncryptedPan = 'encrypted-pan-data'
    vi.mocked(secureapi.post).mockResolvedValue({
      data: { data: { encryptedPan: mockEncryptedPan } },
    })

    const result = await getPan(mockDispatch, mockData)

    expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/view-pan', mockData)
    expect(result).toBe(mockEncryptedPan)
  })

  it('should handle get PAN error', async () => {
    const mockError = new Error('PAN fetch failed')
    vi.mocked(secureapi.post).mockRejectedValue(mockError)

    await getPan(mockDispatch, mockData)

    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'PAN fetch failed', type: 'error' })
    )
    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingPan(false))
  })
})

describe('getBranchCardsByPhonePan', () => {
  const params = 'pan=1234&phoneNumber=%2B254712345678&page=1&size=10'

  it('should successfully fetch branch cards', async () => {
    const mockResponse = createMockCardResponse()
    vi.mocked(secureapi.get).mockResolvedValue({ data: { data: mockResponse } })

    await getBranchCardsByPhonePan(mockDispatch, params)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingBranchCards(true))
    expect(secureapi.get).toHaveBeenCalledWith(`backoffice-bff/cards/branch-query?${params}`)
    expect(mockDispatch).toHaveBeenCalledWith(setBranchCardsList(mockResponse.data))
    expect(mockDispatch).toHaveBeenCalledWith(
      setBranchCardsListPagination({
        totalElements: mockResponse.totalElements,
        totalNumberOfPages: mockResponse.totalNumberOfPages,
        pageNumber: mockResponse.pageNumber,
        pageSize: mockResponse.pageSize,
      })
    )
    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingBranchCards(false))
  })

  it('should handle fetch branch cards error', async () => {
    const mockError = new Error('Branch cards fetch failed')
    vi.mocked(secureapi.get).mockRejectedValue(mockError)

    await getBranchCardsByPhonePan(mockDispatch, params)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingBranchCards(false))
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Branch cards fetch failed', type: 'error' })
    )
  })
})

describe('resetCardPinRetryCounter', () => {
  const mockData = {
    comments: 'Reset retries',
    cardIdentifierId: 'card123',
    cardIdentifierType: 'cardId',
    countryCode: 'KE',
  }

  it('should successfully reset PIN retry counter for super user', async () => {
    const mockResponse = { data: { success: true } }
    vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

    await resetCardPinRetryCounter(mockDispatch, mockData, 'super')

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingResetPinRetries(true))
    expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/reset-pin-try-counter', mockData)
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Reset Card PIN Retries request was successful',
        type: 'success',
      })
    )
    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingResetPinRetries(false))
  })

  it('should successfully send reset PIN retry counter request for make user', async () => {
    const mockResponse = { data: { success: true } }
    vi.mocked(secureapi.post).mockResolvedValue(mockResponse)

    await resetCardPinRetryCounter(mockDispatch, mockData, 'make')

    expect(secureapi.post).toHaveBeenCalledWith('backoffice-bff/cards/reset-pin-try-counter/make', mockData)
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Reset Card PIN Retries request was submitted successfully for approval',
        type: 'success',
      })
    )
  })

  it('should handle reset PIN retry counter error', async () => {
    const mockError = new Error('Reset failed')
    vi.mocked(secureapi.post).mockRejectedValue(mockError)

    await resetCardPinRetryCounter(mockDispatch, mockData, 'super')

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingResetPinRetries(false))
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Reset failed', type: 'error' })
    )
  })
})

describe('completeResetCardPinRetryCounter', () => {
  const data = { comments: 'Approved' }
  const approvalId = 'approval123'

  it('should successfully complete reset PIN retry counter approval', async () => {
    const mockResponse = { data: { data: { success: true } } }
    vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

    const result = await completeResetCardPinRetryCounter(mockDispatch, data, approvalId, 'approve')

    expect(secureapi.put).toHaveBeenCalledWith(
      `backoffice-bff/cards/reset-pin-try-counter/approve/${approvalId}`,
      data
    )
    expect(result).toEqual({ success: true })
  })

  it('should successfully complete reset PIN retry counter rejection', async () => {
    const mockResponse = { data: { data: { success: true } } }
    vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

    const result = await completeResetCardPinRetryCounter(mockDispatch, data, approvalId, 'reject')

    expect(secureapi.put).toHaveBeenCalledWith(
      `backoffice-bff/cards/reset-pin-try-counter/reject/${approvalId}`,
      data
    )
    expect(result).toEqual({ success: true })
  })

  it('should handle complete reset PIN retry counter error', async () => {
    const mockError = new Error('Completion failed')
    vi.mocked(secureapi.put).mockRejectedValue(mockError)

    await completeResetCardPinRetryCounter(mockDispatch, data, approvalId, 'approve')

    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Completion failed', type: 'error' })
    )
  })
})

describe('completeCardSetPin', () => {
  const approvalId = 'approval123'
  const data = { comments: 'Approved' }

  it('should successfully approve card set PIN', async () => {
    const mockResponse = { data: { success: true } }
    vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

    await completeCardSetPin(mockDispatch, approvalId, 'approve', data)

    expect(secureapi.put).toHaveBeenCalledWith(
      `backoffice-bff/cards/set-pin/approve/${approvalId}`,
      data
    )
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Set pin credit card request has been approved. Customer will be notified via an sms that will include their PIN.',
        type: 'success',
      })
    )
  })

  it('should successfully reject card set PIN', async () => {
    const mockResponse = { data: { success: true } }
    vi.mocked(secureapi.put).mockResolvedValue(mockResponse)

    await completeCardSetPin(mockDispatch, approvalId, 'reject', data)

    expect(secureapi.put).toHaveBeenCalledWith(
      `backoffice-bff/cards/set-pin/reject/${approvalId}`,
      data
    )
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Set pin credit card request has been rejected',
        type: 'success',
      })
    )
  })

  it('should handle complete card set PIN error', async () => {
    const mockError = new Error('Set PIN completion failed')
    vi.mocked(secureapi.put).mockRejectedValue(mockError)

    await completeCardSetPin(mockDispatch, approvalId, 'approve', data)

    expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(false))
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Set PIN completion failed', type: 'error' })
    )
  })
})
})
