import { describe, it, expect, beforeEach } from 'vitest'
import cardsReducer, {
  setIsLoadingCards,
  setIsLoadingSingleCard,
  setIsLoadingActivateCard,
  setIsLoadingSetPinCard,
  setIsLoadingResetPinRetries,
  setIsBranchListView,
  setCreditCardsList,
  setSelectedCardStatus,
  setSelectedCardToView,
  setCardResponse,
  setIsLoadingBranchCards,
  setBranchCardsList,
  setBranchCardsListPagination,
  setCurrentTabIndex,
} from '@/store/reducers/cards'
import { InitialState, ICardStatus } from '@/store/interfaces/CardInterfaces'
import { 
  createMockCreditCard, 
  createMockListCreditCard, 
  createMockCardResponse 
} from '../mocks/data'

describe('Cards Reducer', () => {
  let initialState: InitialState

  beforeEach(() => {
    initialState = {
      isLoadingCards: false,
      isLoadingSingleCard: false,
      isLoadingActivateCard: false,
      isLoadingSetPinCard: false,
      isLoadingResetPinRetries: false,
      cardsError: false,
      cardsSuccess: false,
      isLoadingPan: false,
      creditCardsList: [],
      creditCardResponse: {} as any,
      selectedCardStatus: 'inactive',
      selectedCardToView: {} as any,
      isBranchListView: false,
      isLoadingBranchCards: false,
      branchCardsList: [],
      branchCardListPagination: {} as any,
      currentTabIndex: 0,
    }
  })

  describe('initial state', () => {
    it('should return the initial state', () => {
      const result = cardsReducer(undefined, { type: 'unknown' })
      expect(result).toEqual(initialState)
    })
  })

  describe('loading states', () => {
    it('should handle setIsLoadingCards', () => {
      const action = setIsLoadingCards(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingCards).toBe(true)
    })

    it('should handle setIsLoadingSingleCard', () => {
      const action = setIsLoadingSingleCard(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingSingleCard).toBe(true)
    })

    it('should handle setIsLoadingActivateCard', () => {
      const action = setIsLoadingActivateCard(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingActivateCard).toBe(true)
    })

    it('should handle setIsLoadingSetPinCard', () => {
      const action = setIsLoadingSetPinCard(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingSetPinCard).toBe(true)
    })

    it('should handle setIsLoadingResetPinRetries', () => {
      const action = setIsLoadingResetPinRetries(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingResetPinRetries).toBe(true)
    })

    it('should handle setIsLoadingBranchCards', () => {
      const action = setIsLoadingBranchCards(true)
      const result = cardsReducer(initialState, action)
      expect(result.isLoadingBranchCards).toBe(true)
    })
  })

  describe('cards data management', () => {
    it('should handle setCreditCardsList', () => {
      const mockCards = [createMockCreditCard(), createMockCreditCard()]
      const action = setCreditCardsList(mockCards)
      const result = cardsReducer(initialState, action)
      expect(result.creditCardsList).toEqual(mockCards)
    })

    it('should handle setSelectedCardToView', () => {
      const mockCard = createMockCreditCard()
      const action = setSelectedCardToView(mockCard)
      const result = cardsReducer(initialState, action)
      expect(result.selectedCardToView).toEqual(mockCard)
    })

    it('should handle setCardResponse', () => {
      const mockResponse = createMockCardResponse()
      const action = setCardResponse(mockResponse)
      const result = cardsReducer(initialState, action)
      expect(result.creditCardResponse).toEqual(mockResponse)
    })

    it('should handle setSelectedCardStatus', () => {
      const status: ICardStatus = 'active'
      const action = setSelectedCardStatus(status)
      const result = cardsReducer(initialState, action)
      expect(result.selectedCardStatus).toBe(status)
    })
  })

  describe('branch cards management', () => {
    it('should handle setBranchCardsList', () => {
      const mockBranchCards = [createMockListCreditCard(), createMockListCreditCard()]
      const action = setBranchCardsList(mockBranchCards)
      const result = cardsReducer(initialState, action)
      expect(result.branchCardsList).toEqual(mockBranchCards)
    })

    it('should handle setBranchCardsListPagination', () => {
      const mockPagination = {
        pageNumber: 2,
        pageSize: 15,
        totalElements: 30,
        totalNumberOfPages: 2,
      }
      const action = setBranchCardsListPagination(mockPagination)
      const result = cardsReducer(initialState, action)
      expect(result.branchCardListPagination).toEqual(mockPagination)
    })

    it('should handle setIsBranchListView', () => {
      const action = setIsBranchListView(true)
      const result = cardsReducer(initialState, action)
      expect(result.isBranchListView).toBe(true)
    })
  })

  describe('UI state management', () => {
    it('should handle setCurrentTabIndex', () => {
      const action = setCurrentTabIndex(2)
      const result = cardsReducer(initialState, action)
      expect(result.currentTabIndex).toBe(2)
    })
  })

  describe('state immutability', () => {
    it('should not mutate the original state when updating loading states', () => {
      const action = setIsLoadingCards(true)
      const result = cardsReducer(initialState, action)
      expect(result).not.toBe(initialState)
      expect(initialState.isLoadingCards).toBe(false)
    })

    it('should not mutate the original state when updating cards list', () => {
      const mockCards = [createMockCreditCard()]
      const action = setCreditCardsList(mockCards)
      const result = cardsReducer(initialState, action)
      expect(result).not.toBe(initialState)
      expect(initialState.creditCardsList).toEqual([])
    })
  })

  describe('multiple actions', () => {
    it('should handle multiple loading state changes', () => {
      let state = cardsReducer(initialState, setIsLoadingCards(true))
      state = cardsReducer(state, setIsLoadingSingleCard(true))
      state = cardsReducer(state, setIsLoadingActivateCard(true))

      expect(state.isLoadingCards).toBe(true)
      expect(state.isLoadingSingleCard).toBe(true)
      expect(state.isLoadingActivateCard).toBe(true)
    })

    it('should handle setting cards data and then updating loading state', () => {
      const mockCards = [createMockCreditCard()]
      let state = cardsReducer(initialState, setCreditCardsList(mockCards))
      state = cardsReducer(state, setIsLoadingCards(false))

      expect(state.creditCardsList).toEqual(mockCards)
      expect(state.isLoadingCards).toBe(false)
    })
  })

  describe('edge cases', () => {
    it('should handle empty cards list', () => {
      const action = setCreditCardsList([])
      const result = cardsReducer(initialState, action)
      expect(result.creditCardsList).toEqual([])
    })

    it('should handle setting card status to all valid values', () => {
      const statuses: ICardStatus[] = ['active', 'inactive', 'blocked', 'unblocked']
      
      statuses.forEach(status => {
        const action = setSelectedCardStatus(status)
        const result = cardsReducer(initialState, action)
        expect(result.selectedCardStatus).toBe(status)
      })
    })

    it('should handle setting tab index to zero', () => {
      const action = setCurrentTabIndex(0)
      const result = cardsReducer(initialState, action)
      expect(result.currentTabIndex).toBe(0)
    })
  })
})
