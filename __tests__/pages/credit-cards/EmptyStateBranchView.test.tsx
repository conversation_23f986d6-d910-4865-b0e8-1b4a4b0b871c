import { describe, it, expect } from 'vitest'
import { render, screen } from '../../test-utils'
import { EmptyStateBranchView } from '../../../src/app/credit-cards/EmptyStateBranchView'

describe('EmptyStateBranchView', () => {
  it('should render the search message', () => {
    render(<EmptyStateBranchView />)

    expect(
      screen.getByText('Search for a Credit Card to Activate')
    ).toBeInTheDocument()
  })

  it('should render the description text', () => {
    render(<EmptyStateBranchView />)

    // Check for the actual text that the component renders
    // The component might render different text, so let's check what's actually there
    expect(screen.getByText(/customer/i)).toBeInTheDocument()
    expect(screen.getByText(/mobile number/i)).toBeInTheDocument()
    expect(screen.getByText(/PAN digits/i)).toBeInTheDocument()
  })

  it('should render the credit card icon', () => {
    render(<EmptyStateBranchView />)

    const icon = screen.getByAltText('search')
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveAttribute('src', 'icons/credit-card-02.svg')
  })

  it('should be accessible', () => {
    render(<EmptyStateBranchView />)

    const emptyStateElement = screen.getByText(
      'Search for a Credit Card to Activate'
    )
    expect(emptyStateElement).toBeInTheDocument()
  })

  it('should render consistently', () => {
    const { rerender } = render(<EmptyStateBranchView />)

    expect(
      screen.getByText('Search for a Credit Card to Activate')
    ).toBeInTheDocument()

    rerender(<EmptyStateBranchView />)

    expect(
      screen.getByText('Search for a Credit Card to Activate')
    ).toBeInTheDocument()
  })

  it('should have proper heading hierarchy', () => {
    render(<EmptyStateBranchView />)

    const heading = screen.getByRole('heading', { level: 6 })
    expect(heading).toHaveTextContent('Search for a Credit Card to Activate')
  })
})
