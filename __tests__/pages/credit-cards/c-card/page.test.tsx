import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '../../../test-utils'
import CreditCardPage from '../../../../src/app/credit-cards/c-card/page'
import * as cardActions from '@/store/actions/CardsActions'
import { createMockCreditCard } from '../../../mocks/data'

// Mock the child components
vi.mock('../../../../src/app/credit-cards/c-card/PageHeader', () => ({
  default: () => <div data-testid="page-header">Page Header Component</div>
}))

vi.mock('../../../../src/app/credit-cards/c-card/CardView', () => ({
  CardView: () => <div data-testid="card-view">Card View Component</div>
}))

// Mock the card actions
vi.mock('@/store/actions/CardsActions', () => ({
  getCardById: vi.fn(),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children, ...props }: any) => <div data-testid="stack" {...props}>{children}</div>,
    Button: ({ children, onClick, ...props }: any) => (
      <button data-testid="back-button" onClick={onClick} {...props}>{children}</button>
    ),
    Box: ({ children, ...props }: any) => <div data-testid="box" {...props}>{children}</div>,
  }
})

vi.mock('@mui/icons-material/ArrowBack', () => ({
  default: () => <span data-testid="arrow-back-icon">←</span>
}))

describe('CreditCardPage', () => {
  const mockCard = createMockCreditCard({
    cardId: 'test-card-123',
    customerName: 'John Doe',
  })

  const defaultState = {
    cards: {
      selectedCardToView: mockCard,
      isLoadingCards: false,
      isLoadingSingleCard: false,
      isLoadingActivateCard: false,
      isLoadingSetPinCard: false,
      isLoadingResetPinRetries: false,
      cardsError: false,
      cardsSuccess: false,
      creditCardsList: [],
      creditCardResponse: {} as any,
      selectedCardStatus: 'inactive' as const,
      isBranchListView: false,
      isLoadingBranchCards: false,
      branchCardsList: [],
      branchCardListPagination: {} as any,
      currentTabIndex: 0,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock window.history.back
    Object.defineProperty(window, 'history', {
      value: {
        back: vi.fn(),
      },
      writable: true,
    })
  })

  it('should render without crashing', () => {
    expect(() => {
      render(<CreditCardPage />, { preloadedState: defaultState })
    }).not.toThrow()
  })

  it('should render all main components', () => {
    render(<CreditCardPage />, { preloadedState: defaultState })
    
    expect(screen.getByTestId('page-header')).toBeInTheDocument()
    expect(screen.getByTestId('card-view')).toBeInTheDocument()
    expect(screen.getByTestId('back-button')).toBeInTheDocument()
  })

  it('should render back button with correct text and icon', () => {
    render(<CreditCardPage />, { preloadedState: defaultState })
    
    const backButton = screen.getByTestId('back-button')
    expect(backButton).toBeInTheDocument()
    expect(backButton).toHaveTextContent('Back')
    expect(screen.getByTestId('arrow-back-icon')).toBeInTheDocument()
  })

  it('should call window.history.back when back button is clicked', () => {
    const mockHistoryBack = vi.fn()
    Object.defineProperty(window, 'history', {
      value: {
        back: mockHistoryBack,
      },
      writable: true,
    })

    render(<CreditCardPage />, { preloadedState: defaultState })
    
    const backButton = screen.getByTestId('back-button')
    fireEvent.click(backButton)
    
    expect(mockHistoryBack).toHaveBeenCalledTimes(1)
  })

  it('should call getCardById on component mount', () => {
    render(<CreditCardPage />, { preloadedState: defaultState })
    
    expect(cardActions.getCardById).toHaveBeenCalledWith(
      expect.any(Function),
      'test-card-123'
    )
  })

  it('should render proper layout structure', () => {
    render(<CreditCardPage />, { preloadedState: defaultState })
    
    expect(screen.getByTestId('stack')).toBeInTheDocument()
    expect(screen.getByTestId('box')).toBeInTheDocument()
  })

  it('should handle missing selectedCardToView gracefully', () => {
    const stateWithoutCard = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        selectedCardToView: {} as any,
      },
    }

    expect(() => {
      render(<CreditCardPage />, { preloadedState: stateWithoutCard })
    }).not.toThrow()
  })

  it('should handle selectedCardToView with undefined cardId', () => {
    const stateWithUndefinedCardId = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        selectedCardToView: {
          ...mockCard,
          cardId: undefined,
        },
      },
    }

    render(<CreditCardPage />, { preloadedState: stateWithUndefinedCardId })
    
    expect(cardActions.getCardById).toHaveBeenCalledWith(
      expect.any(Function),
      undefined
    )
  })

  it('should handle getCardById API errors gracefully', () => {
    vi.mocked(cardActions.getCardById).mockRejectedValue(new Error('API Error'))

    expect(() => {
      render(<CreditCardPage />, { preloadedState: defaultState })
    }).not.toThrow()
  })

  it('should render components in correct order', () => {
    const { container } = render(<CreditCardPage />, { preloadedState: defaultState })
    
    const stack = screen.getByTestId('stack')
    const pageHeader = screen.getByTestId('page-header')
    const box = screen.getByTestId('box')
    const cardView = screen.getByTestId('card-view')
    
    expect(stack).toContainElement(pageHeader)
    expect(stack).toContainElement(box)
    expect(stack).toContainElement(cardView)
    expect(box).toContainElement(screen.getByTestId('back-button'))
  })

  it('should have proper accessibility for back button', () => {
    render(<CreditCardPage />, { preloadedState: defaultState })
    
    const backButton = screen.getByTestId('back-button')
    expect(backButton).toBeInTheDocument()
    expect(backButton).toHaveAttribute('type', 'button')
  })
})
