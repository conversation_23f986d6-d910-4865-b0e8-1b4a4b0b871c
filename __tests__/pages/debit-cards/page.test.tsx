import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../test-utils'
import DebitCardPage from '../../../src/app/debit-cards/page'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children, ...props }: any) => <div data-testid="stack" {...props}>{children}</div>,
  }
})

describe('DebitCardPage', () => {
  it('should render without crashing', () => {
    expect(() => {
      render(<DebitCardPage />)
    }).not.toThrow()
  })

  it('should render the page title', () => {
    render(<DebitCardPage />)
    
    expect(screen.getByText('Debit Cards')).toBeInTheDocument()
  })

  it('should render within a Stack component', () => {
    render(<DebitCardPage />)
    
    expect(screen.getByTestId('stack')).toBeInTheDocument()
    expect(screen.getByTestId('stack')).toHaveTextContent('Debit Cards')
  })

  it('should have correct text content', () => {
    render(<DebitCardPage />)
    
    const stackElement = screen.getByTestId('stack')
    expect(stackElement).toHaveTextContent('Debit Cards')
    expect(stackElement.textContent).toBe('Debit Cards')
  })

  it('should render consistently on multiple renders', () => {
    const { rerender } = render(<DebitCardPage />)
    
    expect(screen.getByText('Debit Cards')).toBeInTheDocument()
    
    rerender(<DebitCardPage />)
    
    expect(screen.getByText('Debit Cards')).toBeInTheDocument()
  })

  it('should have proper component structure', () => {
    const { container } = render(<DebitCardPage />)
    
    expect(container.firstChild).toHaveAttribute('data-testid', 'stack')
    expect(container.firstChild).toHaveTextContent('Debit Cards')
  })

  it('should be accessible', () => {
    render(<DebitCardPage />)
    
    const pageContent = screen.getByText('Debit Cards')
    expect(pageContent).toBeInTheDocument()
    expect(pageContent).toBeVisible()
  })

  it('should not have any interactive elements', () => {
    render(<DebitCardPage />)
    
    const buttons = screen.queryAllByRole('button')
    const links = screen.queryAllByRole('link')
    const inputs = screen.queryAllByRole('textbox')
    
    expect(buttons).toHaveLength(0)
    expect(links).toHaveLength(0)
    expect(inputs).toHaveLength(0)
  })

  it('should render as a simple static component', () => {
    const { container } = render(<DebitCardPage />)
    
    expect(container.children).toHaveLength(1)
    expect(container.firstChild).toHaveTextContent('Debit Cards')
  })

  it('should match expected DOM structure', () => {
    const { container } = render(<DebitCardPage />)
    
    expect(container.innerHTML).toContain('Debit Cards')
    expect(container.querySelector('[data-testid="stack"]')).toBeInTheDocument()
  })
})
