import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { Button, Menu, MenuItem } from '@mui/material'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { useAppDispatch } from '@/store'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'
import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { getCardById } from '@/store/actions'
import ResolvedCardRequestDetails from '../Drawers/ResolvedCardRequestsDetails'

export const AllApprovalRequestsMoreMenu = ({
  request,
}: {
  request: ICardApprovalRequest
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()
  const router = useRouter()
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleView = async (request: ICardApprovalRequest) => {
    if (request.status === 'PENDING') {
      await ApprovalRequestRouting(request, dispatch, router)
      handleClose()
    } else {
      if (request.entityId) {
        await getCardById(dispatch, request.entityId)
      }
      handleClose()
      router.push('/credit-cards/c-card')
    }
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : 'false'}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>

      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
        slotProps={{
          root: {},
          paper: {
            elevation: 0,
            sx: {
              boxShadow:
                '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
            },
          },
        }}
        disableAutoFocusItem
        disableRestoreFocus
      >
        <ResolvedCardRequestDetails request={request} />
        <AccessControlWrapper
          rights={[
            ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_CARDS,
            ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_CARDS,
          ]}
        >
          <MenuItem onClick={() => handleView(request)}>
            {request.status === 'PENDING' ? 'Review Request' : 'Go To Module'}
          </MenuItem>
        </AccessControlWrapper>
      </Menu>
    </>
  )
}
