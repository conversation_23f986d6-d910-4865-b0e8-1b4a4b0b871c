import { Button } from '@mui/material'
import React, { useState } from 'react'
import { Dialog } from '@dtbx/ui/components/Overlay'
import {
  activateCards,
  getCardById,
  resetCardPin,
  resetCardPinRetryCounter,
} from '@/store/actions/CardsActions'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  HasAccessToRights,
} from '@dtbx/store/utils'
import { ICreditCard } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { setNotification } from '@dtbx/store/reducers'
import { LoadingButton } from '@dtbx/ui/components'

export const ResetPinRetriesCard = ({ card }: { card: ICreditCard }) => {
  const [open, setOpen] = useState(false)
  const dispatch = useAppDispatch()
  const { isLoadingResetPinRetries } = useAppSelector((state) => state.cards)

  const reasons = [
    'Customer Request – Verified Identity',
    'System Error or Timeout',
    'Fraud Flag Cleared',
    'Manual Override – Supervisor Approval',
    'Other',
  ]

  const operationConfig = {
    buttonText: 'Reset PIN Retries',
    title: 'Reset PIN Retries',
    reasons: reasons,
    description: 'Select a reason to reset the PIN retries?',
    defaultComment: 'Reset Card PIN Retries',
  }

  const handleOperation = async (reasons: string[]) => {
    const payload = {
      cardIdentifierId: card.cardId,
      cardIdentifierType: 'EXID',
      countryCode: 'KE',
      comments: reasons.join(',') || 'Card PIN Retries Reset',
    }

    if (HasAccessToRights(['SUPER_CARDS_RESET_PIN_TRY_COUNTER'])) {
      await resetCardPinRetryCounter(dispatch, payload, 'super')
    } else if (HasAccessToRights(['MAKE_CARDS_RESET_PIN_TRY_COUNTER'])) {
      await resetCardPinRetryCounter(dispatch, payload, 'make')
    }
    await getCardById(dispatch, card.cardId)
    setOpen(false)
  }

  const handleButtonClick = () => {
    setOpen(true)
  }

  return (
    <>
      <AccessControlWrapper rights={[...ACCESS_CONTROLS.RESET_PIN_TRY_COUNTER]}>
        <Button
          variant="contained"
          onClick={handleButtonClick}
          loading={isLoadingResetPinRetries}
          sx={{ textWrap: 'nowrap' }}
        >
          Reset PIN Retries
        </Button>
      </AccessControlWrapper>

      <Dialog
        buttonText={operationConfig.buttonText}
        title={operationConfig.title}
        open={open}
        isLoading={isLoadingResetPinRetries}
        descriptionText={operationConfig.description}
        setOpen={setOpen}
        buttonProps={{
          color: '#EB0045',
        }}
        reasons={operationConfig.reasons}
        onClick={handleOperation}
      />
    </>
  )
}
