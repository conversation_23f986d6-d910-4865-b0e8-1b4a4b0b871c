'use client'

import { Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { CardHeaderIcon } from '@dtbx/ui/icons'
import { useAppDispatch } from '@/store'
import { setIsBranchListView } from '@/store/reducers'
import { HasAccessToRights } from '@dtbx/store/utils'

export default function CreditCardsLayout(props: {
  children: React.ReactNode
}) {
  const dispatch = useAppDispatch()


  const hasBranchViewRights = HasAccessToRights(['BRANCH_VIEW_CARDS'])

  useEffect(() => {
    if (hasBranchViewRights) {
      // User has BRANCH_VIEW_CARDS right, show BranchViewList
      dispatch(setIsBranchListView(true))
    } else {
      // User doesn't have the right, show CreditCardsList
      dispatch(setIsBranchListView(false))
    }
  }, [dispatch, hasBranchViewRights])
  return (
    <Stack>
      <Stack direction="row" justifyContent="space-between">
        <Stack
          sx={{
            marginLeft: '2%',
            marginTop: '0.2%',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '8px',
            py: '8px',
          }}
        >
          <CardHeaderIcon width="30" height="28" />
          <Typography variant="h5">Credit Cards</Typography>
        </Stack>
      </Stack>

      <Stack>{props.children}</Stack>
    </Stack>
  )
}
