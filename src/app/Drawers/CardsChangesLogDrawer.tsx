'use client'
import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  DialogTitle,
  IconButton,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Avatar,
  Chip,
  Collapse,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomSkeleton, ReadOnlyTypography } from '@dtbx/ui/components'
import { CustomTableCell, CustomTableHeader } from '@dtbx/ui/components/Table'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { SearchRounded } from '@mui/icons-material'
import { useAppSelector } from '@/store'
import { formatTimestamp } from '@dtbx/store/utils'
import { sentenceCase } from 'tiny-case'
import { CardsIcon } from '@dtbx/ui/components/SvgIcons'

export const CardsChangesLogDrawer = () => {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedApproval, setSelectedApproval] = useState<any>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const { branchApprovalRequests } = useAppSelector(
    (state) => state.approvals
  )
  const header: IHeadCell[] = [
    {
      id: 'event',
      label: 'Event Type',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'status',
      label: 'Status',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'maker',
      label: 'Maker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'makerTimestamp',
      label: 'Maker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'checker',
      label: 'Checker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'checkerTimestamp',
      label: 'Checker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'actions',
      label: 'Actions',
      alignCenter: false,
      alignRight: false,
    },
  ]

  const resolvedHeader: IHeadCell[] = [
    { id: 'field', label: 'Field', alignCenter: false, alignRight: false },

    {
      id: 'oldValue',
      label: 'Old Value',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'newValue',
      label: 'New Value',
      alignCenter: false,
      alignRight: false,
    },
  ]

  // Filter approval requests based on search term
  const filteredApprovals = branchApprovalRequests?.filter((approval) =>
    approval.makerCheckerType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.maker.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (approval.checker && approval.checker.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || []

  const handleClose = (_e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
    setDetailsOpen(false)
    setSelectedApproval(null)
  }

  const handleViewApproval = (approval: any) => {
    setSelectedApproval(approval)
    setDetailsOpen(true)
  }

  const handleCloseDetails = () => {
    setDetailsOpen(false)
    setSelectedApproval(null)
  }

  const getStatusChipProps = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return {
          color: '#12B76A' as const,
          backgroundColor: '#ECFDF3',
          label: 'Approved'
        }
      case 'REJECTED':
        return {
          color: '#F04438' as const,
          backgroundColor: '#FEF3F2',
          label: 'Rejected'
        }
      case 'PENDING':
        return {
          color: '#F79009' as const,
          backgroundColor: '#FFFAEB',
          label: 'Pending'
        }
      default:
        return {
          color: '#667085' as const,
          backgroundColor: '#F2F4F7',
          label: sentenceCase(status)
        }
    }
  }

  const isLoading = false

  return (
    <>
      {' '}
      {branchApprovalRequests && branchApprovalRequests.length > 0 && (
        <Button
          variant="outlined"
          onClick={() => setOpen(!open)}
          sx={{
            textWrap: 'noWrap',
            border: '1px solid #D0D5DD',
            borderRadius: '4px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            height: '2.5rem',
            color: '#555C61',
            fontSize: '15px',
            fontWeight: '500',
            display: 'flex',
          }}
        >
          Changes Log
        </Button>
      )}
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
      >
        {' '}
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
            height: '3.5rem',
            display: 'flex',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                py: '5px',
              }}
            >
              <Typography variant="subtitle2" color={'primary.main'}>
                Changes Log
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          sx={{
            height: 'calc(100vh - 3.5rem)',
            overflow: 'hidden'
          }}
        >
          <Stack
            sx={{
              flex: {
                xs: '1',
                sm: detailsOpen ? '0 0 60%' : '1'
              },
              padding: { xs: 1.5, sm: 1.5, md: '1.5rem 2.5rem' },
              borderRight: { sm: detailsOpen ? '1px solid #EAECF0' : 'none' },
              borderBottom: { xs: '1px solid #EAECF0', sm: 'none' },
              overflow: 'hidden',
              transition: 'flex 0.3s ease-in-out'
            }}
          >
            <CustomSearchInput
              sx={{
                width: { xs: '100%', sm: '60%', md: '40%' },
                mb: 2,
                borderRadius: '4px',
                '& fieldset': {
                  border: '1px solid #D0D5DD !important',
                },
              }}
              startAdornment={<SearchRounded />}
              placeholder="Search by event type, maker, status..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Stack sx={{ flex: 1, overflow: 'hidden' }}>
              <Paper
                elevation={0}
                sx={{
                  border: '1px solid #EAECF0',
                  boxShadow:
                    '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                  height: '100%',
                }}
              >
                <TableContainer
                  component={Paper}
                  elevation={0}
                  sx={{
                    boxShadow: 'none',
                    height: '100%',
                    overflow: 'auto',
                  }}
                >
                  {!isLoading ? (
                    filteredApprovals?.length > 0 ? (
                      <Table
                        sx={{ minWidth: 650 }}
                        aria-label="approval requests table"
                      >
                        <CustomTableHeader
                          order={'desc'}
                          orderBy={''}
                          rowCount={0}
                          headLabel={[...header]}
                          numSelected={0}
                        />
                        <TableBody>
                          {filteredApprovals.map((approval) => {
                            return (
                              <TableRow
                                key={approval.id}
                                hover
                                sx={{
                                  backgroundColor: selectedApproval?.id === approval.id ? '#F9FAFB' : 'inherit',
                                  cursor: 'pointer'
                                }}
                              >
                                <TableCell>{approval.makerCheckerType.name}</TableCell>
                                <TableCell>
                                  <Chip
                                    label={getStatusChipProps(approval.status).label}
                                    sx={{
                                      color: getStatusChipProps(approval.status).color,
                                      backgroundColor: getStatusChipProps(approval.status).backgroundColor,
                                      fontWeight: 500,
                                      fontSize: '0.75rem',
                                      height: '24px',
                                      '& .MuiChip-label': {
                                        px: 1
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell>{approval.maker}</TableCell>
                                <TableCell>{formatTimestamp(approval.dateCreated)}</TableCell>
                                <TableCell>{approval.checker || 'N/A'}</TableCell>
                                <TableCell>
                                  {approval.dateModified !== approval.dateCreated
                                    ? formatTimestamp(approval.dateModified)
                                    : 'N/A'}
                                </TableCell>
                                <TableCell>
                                  <Button
                                    variant="outlined"
                                    sx={{
                                      border: '1px solid #D0D5DD',
                                      height: '2.5rem',
                                      boxShadow:
                                        '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                                      color: '#555C61',
                                      fontSize: '15px',
                                      fontWeight: '500',
                                    }}
                                    onClick={() => handleViewApproval(approval)}
                                  >
                                    View
                                  </Button>
                                </TableCell>
                              </TableRow>
                            )
                          })}
                        </TableBody>
                      </Table>
                    ) : (
                      <Box sx={{ p: 4, textAlign: 'center' }}>
                        <Typography variant="h6" color="text.secondary">
                          No approval requests found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {searchTerm ? 'Try adjusting your search criteria' : 'No approval history available for this card'}
                        </Typography>
                      </Box>
                    )
                  ) : (
                    <CustomSkeleton
                      variant="rectangular"
                      animation="wave"
                      sx={{
                        height: '70vh',
                        width: '100%',
                      }}
                    />
                  )}
                </TableContainer>
              </Paper>
            </Stack>
          </Stack>

         
          <Collapse
            in={detailsOpen}
            orientation="horizontal"
            sx={{
              '& .MuiCollapse-wrapper': {
                width: detailsOpen ? { xs: '100%', sm: '40vw' } : 0,
              },
              '& .MuiCollapse-wrapperInner': {
                width: { xs: '100%', sm: '40vw' },
              }
            }}
          >
            <Stack
              sx={{
                width: { xs: '100%', sm: '40vw' },
                height: '100%',
                padding: { xs: 1.5, sm: 1.5, md: '1.5rem 2.5rem' },
                overflow: 'auto',
                backgroundColor: '#FAFBFC',
                borderLeft: { sm: '1px solid #EAECF0' },
                borderTop: { xs: '1px solid #EAECF0', sm: 'none' }
              }}
            >
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mb: 2, pb: 2, borderBottom: '1px solid #EAECF0' }}
              >
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#101828' }}>
                  Request Details
                </Typography>
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CloseIcon fontSize="small" />}
                    sx={{
                      border: '1px solid #D0D5DD',
                      color: '#555C61',
                      fontSize: '14px',
                      fontWeight: '500',
                      height: '32px'
                    }}
                    onClick={handleCloseDetails}
                  >
                    Close
                  </Button>
                </Stack>
              </Stack>

              {selectedApproval ? (
                <Stack
                  sx={{
                    flexDirection: 'column',
                    gap: '20px',
                  }}
                >

                <ReadOnlyTypography
                  fullWidth
                  label="Approval request type"
                  value={selectedApproval?.makerCheckerType?.name}
                />

                <Stack
                  direction="row"
                  spacing={1}
                  width="100%"
                  p={'0.2rem'}
                  sx={{
                    border: '1px solid #D0D5DD',
                    borderRadius: '0.3rem',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                >
                  <Stack
                    direction="row"
                    alignItems="start"
                    width={'100%'}
                    p={1}
                    gap={2}
                  >
                    <Avatar
                      sx={{
                        backgroundColor: '#E7E8E9',
                        borderRadius: 1,
                        width: '35px',
                        height: '35px',
                      }}
                      alt="Card Icon"
                    >
                      <CardsIcon />
                    </Avatar>
                    <Stack direction="column" justifyContent="center" spacing={0.5}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Typography
                          variant="body1"
                          sx={{ fontWeight: '700', fontSize: '1rem' }}
                        >
                          Credit Card
                        </Typography>
                        <Chip
                          label={getStatusChipProps(selectedApproval?.status).label}
                          sx={{
                            color: getStatusChipProps(selectedApproval?.status).color,
                            backgroundColor: getStatusChipProps(selectedApproval?.status).backgroundColor,
                            fontWeight: 500,
                            fontSize: '0.75rem',
                            height: '20px',
                            '& .MuiChip-label': {
                              px: 1
                            }
                          }}
                        />
                      </Stack>
                      <Typography variant="body1">
                        &nbsp; &#8226; {selectedApproval?.makerCheckerType?.name}
                      </Typography>
                    </Stack>
                  </Stack>
                </Stack>
                <Stack
                  sx={{
                    padding: '0.5rem',
                    border: '1px solid #D0D5DD',
                    gap: '0.5rem',
                    borderRadius: '0.3rem',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                >
                  <Typography variant="body2" fontWeight={600}>
                    Changes Made
                  </Typography>
                  <TableContainer component={Paper} elevation={0}>
                    <Table>
                      <CustomTableHeader
                        order={'desc'}
                        orderBy={''}
                        rowCount={0}
                        headLabel={resolvedHeader}
                        numSelected={0}
                      />
                      <TableBody>
                        {selectedApproval?.diff && selectedApproval.diff.length > 0 ? (
                          selectedApproval.diff.map((diff: any, index: number) => (
                            <TableRow key={index}>
                              <CustomTableCell>{diff.field}</CustomTableCell>
                              <CustomTableCell>
                                {String(diff.oldValue ?? 'N/A')}
                              </CustomTableCell>
                              <CustomTableCell>
                                {String(diff.newValue ?? 'N/A')}
                              </CustomTableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <CustomTableCell colSpan={3} sx={{ textAlign: 'center', py: 2 }}>
                              No changes recorded for this request
                            </CustomTableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Stack>

                <ReadOnlyTypography
                  fullWidth
                  label="Maker"
                  value={selectedApproval?.maker}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Maker timestamp"
                  value={formatTimestamp(selectedApproval?.dateCreated)}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Maker comment"
                  value={selectedApproval?.makerComments || 'No comment provided'}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Checker"
                  value={selectedApproval?.checker || 'N/A'}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Checker timestamp"
                  value={selectedApproval?.dateModified !== selectedApproval?.dateCreated
                    ? formatTimestamp(selectedApproval?.dateModified)
                    : 'N/A'}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Checker comment"
                  value={selectedApproval?.checkerComments || 'No comment provided'}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="Status"
                  value={sentenceCase(selectedApproval?.status)}
                />
              </Stack>
              ) : (
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  textAlign: 'center'
                }}>
                  <Typography variant="body1" color="text.secondary">
                    Select an approval request from the table to view details
                  </Typography>
                </Box>
              )}
            </Stack>
          </Collapse>
        </Stack>
      </Drawer>
    </>
  )
}
