import { Check, CloseRounded } from '@mui/icons-material'
import {
  Ava<PERSON>,
  But<PERSON>,
  Drawer,
  IconButton,
  MenuItem,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { CustomStatusChip } from '@dtbx/ui/components/Chip'

import { ICardApprovalRequest } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { AccessControlWrapper, formatTimestamp } from '@dtbx/store/utils'
;('@/store/actions')
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { CardsIcon, InfoIcon } from '@dtbx/ui/components/SvgIcons'
import { CustomTableHeader, CustomTableCell } from '@dtbx/ui/components/Table'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'

const ResolvedCardRequestDetails = ({
  request,
}: {
  request: ICardApprovalRequest
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)
  const [checkerComments, setCheckerComments] = useState<string>('')
  const [commentsError, setCommentsError] = useState<boolean>(false)

  const [checkerCommentsError, setCheckerCommentsError] = useState(false)

  const { isLoadingActivateCard } = useAppSelector((state) => state.cards)

  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }

  const router = useCustomRouter()
  const resolvedHeader: IHeadCell[] = [
    { id: 'field', label: 'Field', alignCenter: false, alignRight: false },

    {
      id: 'oldValue',
      label: 'Old Value',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'newValue',
      label: 'New Value',
      alignCenter: false,
      alignRight: false,
    },
  ]
  const handleReject = async () => {
    if (checkerComments === '') {
      setCheckerCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `REJECT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
  }
  const handleApprove = async () => {
    if (checkerComments === '') {
      setCheckerCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `ACCEPT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
  }
  const handleGoToModule = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    handleClose(null, 'close')
  }

  return (
    <>
      <MenuItem
        //   onClick={handleSeeRequest}
        onClick={() => setOpen(!open)}
      >
        <Typography>See request summary</Typography>
      </MenuItem>
      <Drawer
        open={open}
        variant="persistent"
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        anchor={'right'}
        onClose={() => handleClose(null, 'close')}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontSize: '18px',
              px: '1vw',
            }}
          >
            Approval request details
          </Typography>
          <IconButton onClick={() => handleClose(null, 'close')}>
            <CloseRounded />
          </IconButton>
        </Stack>
        <Stack sx={{ px: '1vw', py: '2vh', gap: '1rem' }}>
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <ReadOnlyTypography
              fullWidth
              label="Approval request type"
              sx={{}}
              value="maker checker name"
            />
            <Stack
              direction="row"
              spacing={1}
              width="100%"
              p={'0.2rem'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '0.3rem',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
            >
              <Stack
                direction="row"
                alignItems="start"
                width={'100%'}
                p={1}
                gap={2}
              >
                <Avatar
                  sx={{
                    backgroundColor: '#E7E8E9',
                    borderRadius: 1,
                    width: '35px',
                    height: '35px',
                  }}
                  alt={` Icon`}
                >
                  <CardsIcon />
                </Avatar>
                <Stack direction="column" justifyContent="center" spacing={0.5}>
                  <Typography
                    variant="body1"
                    sx={{ fontWeight: '700', fontSize: '1rem' }}
                  >
                    Credit Cards
                  </Typography>
                  <Typography variant="body1">
                    &nbsp; &#8226; {request?.makerCheckerType?.name}{' '}
                  </Typography>
                  <CustomStatusChip label={request.status} />
                </Stack>
              </Stack>
            </Stack>
            <Stack
              sx={{
                padding: '0.5rem',
                border: '1px solid #D0D5DD',
                gap: '0.5rem',
                borderRadius: '0.3rem',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
            >
              <Typography variant="body2" fontWeight={600}>
                Changes Made
              </Typography>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <CustomTableHeader
                    order={'desc'}
                    orderBy={''}
                    rowCount={0}
                    headLabel={resolvedHeader}
                    numSelected={0}
                  />
                  <TableBody>
                    {request?.diff?.map((diff, index) => (
                      <TableRow key={index}>
                        <CustomTableCell>{diff.field}</CustomTableCell>
                        <CustomTableCell>
                          {String(diff.oldValue ?? 'N/A')}
                        </CustomTableCell>
                        <CustomTableCell>
                          {String(diff.newValue ?? 'N/A')}
                        </CustomTableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Stack>
            <ReadOnlyTypography
              fullWidth
              label="Maker"
              sx={{}}
              value={request?.maker}
            />
            <ReadOnlyTypography
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={formatTimestamp(request?.dateCreated)}
            />
            <ReadOnlyTypography
              fullWidth
              label="Maker comment"
              sx={{}}
              value={request?.makerComments}
            />
            {request.status === 'PENDING' ? (
              <TextField
                fullWidth
                multiline
                rows={5}
                label="Checker comment"
                sx={{}}
                value={checkerComments}
                error={checkerCommentsError}
                helperText={
                  checkerCommentsError && 'Checker comments are required'
                }
                onChange={(e) => {
                  if (e.target.value !== '') setCheckerCommentsError(false)
                  setCheckerComments(e.target.value)
                }}
              />
            ) : (
              <ReadOnlyTypography
                fullWidth
                label="Checker comments"
                sx={{}}
                value={request?.checkerComments || 'No comments submitted'}
              />
            )}
          </Stack>
          <Stack direction="row" gap={2}>
            {request.status === 'PENDING' ? (
              <>
                <AccessControlWrapper
                  rights={request?.makerCheckerType?.checkerPermissions?.filter(
                    (perm) => perm.includes('REJECT')
                  )}
                  makerId={request.maker}
                >
                  <Button
                    variant="outlined"
                    fullWidth
                    sx={{
                      height: '40px',
                      background: '#E3E4E4',
                      border: '1px solid #AAADB0',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    disabled={checkerComments === ''}
                    loading={isLoadingActivateCard}
                    onClick={handleReject}
                  >
                    Reject
                  </Button>
                </AccessControlWrapper>
                <AccessControlWrapper
                  rights={request?.makerCheckerType?.checkerPermissions?.filter(
                    (perm) => perm.includes('ACCEPT')
                  )}
                  makerId={request.maker}
                >
                  <Button
                    variant="contained"
                    loading={isLoadingActivateCard}
                    fullWidth
                    sx={{
                      height: '40px',
                    }}
                    disabled={checkerComments === ''}
                    onClick={handleApprove}
                  >
                    Approve
                    <Check sx={{ marginLeft: 1 }} />
                  </Button>
                </AccessControlWrapper>
              </>
            ) : (
              <>
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{
                    height: '40px',
                    background: '#E3E4E4',
                    border: '1px solid #AAADB0',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  loading={isLoadingActivateCard}
                  onClick={(e) => handleClose(e, 'close')}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  loading={isLoadingActivateCard}
                  fullWidth
                  sx={{
                    height: '40px',
                  }}
                  onClick={handleGoToModule}
                >
                  Go To Module
                </Button>
              </>
            )}
          </Stack>
          <Stack
            direction="row"
            gap={'2%'}
            sx={{
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <InfoIcon />
            {request.status === 'PENDING' ? (
              <Typography variant="body2" sx={{ color: '#555C61' }}>
                You are approving or rejecting all the changes made as listed
                above.
              </Typography>
            ) : (
              <Typography variant="body2" sx={{ color: '#555C61' }}>
                You are viewing a summary of changes made above.
              </Typography>
            )}
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ResolvedCardRequestDetails
