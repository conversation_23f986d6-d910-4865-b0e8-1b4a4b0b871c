'use client'
import React, { ReactNode } from 'react'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import AppProvider from '@/store/AppProvider'
import {
  CustomScrollbar,
  InActivity,
} from '@dtbx/ui/components'

import { DashboardLayout } from './DashboardLayout'
import { isLoggedIn } from '@dtbx/store/utils'
import '@dtbx/ui/theme/index.css'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig>
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>
                  <DashboardLayout>{children}</DashboardLayout>
                </InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}
