import { combineReducers } from 'redux'
import {
  authReducer,
  navigation,
  notifications,
  overlays,
} from '@dtbx/store/reducers'
import cardsReducer from './cards'
import approvalsReducer from './approvals'

const rootReducer = combineReducers({
  navigation: navigation,
  notifications: notifications,
  auth: authReducer,
  cards: cardsReducer,
  approvals: approvalsReducer,
  overlay: overlays,
})
export default rootReducer
